import { ApplyOptions } from '@sapphire/decorators';
import { HttpCodes, Route } from '@sapphire/plugin-api';
import { ChannelType, TextChannel } from 'discord.js';
import z from 'zod';
import { TicketChannelEveryonePermissions, TicketChannelUserPermissions } from '../../utils/constants';

const WebhookSchema = z.object({
	message_id: z.string(),
	ticket_id: z.string(),
	message: z.string(),
	user_id: z.string(),
	user_name: z.string(),
	user_email: z.string(),
	contact_id: z.string(),
	contact_name: z.string(),
	contact_email: z.string().optional(),
	contact_identifier: z.string().optional(),
	channel_id: z.string()
});

@ApplyOptions<Route.Options>({ route: '/webhook', methods: ['POST'] })
export class WebhookRoute extends Route {
	public override async run(request: Route.Request, response: Route.Response): Promise<void> {
		let payload: z.infer<typeof WebhookSchema>;

		try {
			payload = (await request.readBodyJson()) as z.infer<typeof WebhookSchema>;
			this.container.logger.debug('recv /webhook (parsed body):', payload);
		} catch (error) {
			this.container.logger.error('recv /webhook (failed to parse body):', error);
			return response.error(HttpCodes.BadRequest);
		}

		this.container.logger.info('recv /webhook');

		if (payload.contact_identifier?.startsWith('custom-')) {
			const ticket = await this.container.tickets.fetch(payload.contact_identifier);

			if (!ticket) return response.error(HttpCodes.NotFound);

			let channel = this.container.client.channels.cache.get(ticket.channelId) as TextChannel | undefined;

			if (!channel) {
				const guild = await this.container.client.guilds.fetch(ticket.guildId);

				if (!guild) return response.error(HttpCodes.NotFound);

				channel = await guild.channels.create({
					parent: ticket.categoryId,
					name: ticket.userId,
					type: ChannelType.GuildText
				});

				await channel.permissionOverwrites.edit(guild.id, TicketChannelEveryonePermissions);

				await channel.permissionOverwrites.edit(ticket.userId, TicketChannelUserPermissions);

				if (!channel) return response.error(HttpCodes.NotFound);

				await this.container.tickets.update(ticket.userId, { channelId: channel.id });
			}

			const message = await this.container.trengo.fetchMessage(payload);

			if (message.status === 200) {
				await channel.send({ content: message.body.message, files: message.body.attachments.map((attachment) => attachment.full_url) });
			} else {
				this.container.logger.error(JSON.stringify(message, null, 2));
			}
		}

		return response.ok(HttpCodes.Accepted);
	}
}
