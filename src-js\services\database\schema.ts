import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';
import z from 'zod';
import * as regex from '../../utils/regex';

export const tickets = sqliteTable('tickets', {
	id: integer('id').primaryKey(),
	userId: text('user_id').unique().notNull(),
	guildId: text('guild_id').unique().notNull(),
	categoryId: text('category_id').unique().notNull(),
	channelId: text('channel_id').unique().notNull(),
	contactId: text('contact_id').unique().notNull(),
	contactName: text('contact_name').notNull()
});

export const TicketSchema = z.object({
	id: z.number(),
	userId: z.string().regex(regex.snowflake),
	guildId: z.string().regex(regex.snowflake),
	categoryId: z.string().regex(regex.snowflake),
	channelId: z.string().regex(regex.snowflake),
	contactId: z.string().regex(regex.contactId),
	contactName: z.string()
});

export type Ticket = z.infer<typeof TicketSchema>;
