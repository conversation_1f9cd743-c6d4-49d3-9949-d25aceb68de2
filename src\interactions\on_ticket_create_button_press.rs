use serenity::all::Interaction;
use serenity::async_trait;
use serenity::prelude::*;

pub struct Handler;

#[async_trait]
impl EventHandler for <PERSON><PERSON> {
    async fn interaction_create(&self, ctx: Context, interaction: Interaction) {
        if let Interaction::Component(component) = &interaction {
            if component.data.custom_id.as_str() != "ticket-button" {
                return;
            }
        }
    }
}
