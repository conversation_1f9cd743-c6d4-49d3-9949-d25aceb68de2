import { ApplyOptions } from '@sapphire/decorators';
import { InteractionHandler, InteractionHandlerTypes } from '@sapphire/framework';
import { ButtonInteraction, ChannelType, MessageFlags, TextChannel } from 'discord.js';
import { TicketChannelEveryonePermissions, TicketChannelUserPermissions } from '../../utils/constants';
import { SupportEmbed } from '../components/embeds/support';
import { messages } from '../components/messages';

@ApplyOptions<InteractionHandler.Options>({
	interactionHandlerType: InteractionHandlerTypes.Button
})
export class ButtonHandler extends InteractionHandler {
	public async run(interaction: ButtonInteraction) {
		const guild = interaction.guild;
		const categoryId = (interaction.channel as TextChannel | null)?.parentId;

		if (!guild || !categoryId) return interaction.reply({ content: 'something went wrong!', flags: MessageFlags.Ephemeral });

		const ticket = await this.container.tickets.fetch(interaction.user.id);

		if (ticket) {
			let channel = this.container.client.channels.cache.get(ticket.channelId) as TextChannel | undefined;

			if (channel === undefined) {
				channel = await guild.channels.create({
					parent: categoryId,
					name: ticket.userId,
					type: ChannelType.GuildText
				});

				await channel.permissionOverwrites.edit(guild.id, TicketChannelEveryonePermissions);

				await channel.permissionOverwrites.edit(ticket.userId, TicketChannelUserPermissions);

				await this.container.tickets.update(ticket.userId, { channelId: channel.id });

				await channel.send({ embeds: [SupportEmbed] });

				return interaction.reply({ content: messages.ticket.creationConfirmation, ephemeral: true });
			}

			return interaction.reply({ content: messages.ticket.exists, ephemeral: true });
		} else {
			const channel = await guild.channels.create({
				parent: categoryId,
				name: interaction.user.id,
				type: ChannelType.GuildText
			});

			await channel.permissionOverwrites.edit(guild.id, TicketChannelEveryonePermissions);

			await channel.permissionOverwrites.edit(interaction.user.id, TicketChannelUserPermissions);

			const ticket = await this.container.tickets.insert({
				userId: interaction.user.id,
				guildId: guild.id,
				categoryId: categoryId,
				channelId: channel.id,
				contactId: this.container.trengo.fromUserId(interaction.user.id),
				contactName: interaction.user.username
			});

			if (ticket) {
				await channel.send({ embeds: [SupportEmbed] });

				return interaction.reply({ content: messages.ticket.creationConfirmation, ephemeral: true });
			} else {
				this.container.logger.error(JSON.stringify(ticket, null, 2));

				return interaction.reply({ content: 'something went wrong!', flags: MessageFlags.Ephemeral });
			}
		}
	}

	public override parse(interaction: ButtonInteraction) {
		if (interaction.customId.startsWith('ticket-button')) {
		}

		return this.some();
	}
}
