const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
const base = BigInt(chars.length);

export const base62 = {
	encode(id: string): string {
		let n = BigInt(id);
		let result = '';

		while (n > 0) {
			result = chars[Number(n % base)] + result;
			n = n / base;
		}

		return result || '0';
	},
	decode(id: string): string {
		if (id.length === 0) {
			return '';
		}

		let result = BigInt(0);

		for (let i = 0; i < id.length; i++) {
			const index = chars.indexOf(id[i]);

			if (index === -1) {
				return '';
			}

			result = result * base + BigInt(index);
		}

		return result.toString();
	}
};
