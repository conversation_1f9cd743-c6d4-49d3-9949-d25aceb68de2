{"name": "ttx-discord-trengo-integration", "version": "1.0.0", "main": "dist/mod.js", "author": "@sapphire", "license": "UNLICENSE", "type": "commonjs", "dependencies": {"@sapphire/decorators": "^6.1.1", "@sapphire/discord-utilities": "^3.4.4", "@sapphire/discord.js-utilities": "7.3.2", "@sapphire/fetch": "^3.0.5", "@sapphire/framework": "^5.3.2", "@sapphire/plugin-api": "^8.0.0", "@sapphire/plugin-editable-commands": "^4.0.4", "@sapphire/plugin-logger": "^4.0.2", "@sapphire/plugin-subcommands": "^7.0.1", "@sapphire/time-utilities": "^1.7.14", "@sapphire/type": "^2.6.0", "@sapphire/utilities": "^3.18.1", "@ts-rest/core": "^3.52.1", "better-sqlite3": "^11.10.0", "discord.js": "^14.17.3", "dotenv": "16.5.0", "drizzle-orm": "^0.44.1"}, "devDependencies": {"@sapphire/cli": "^1.9.3", "@sapphire/prettier-config": "^2.0.0", "@sapphire/ts-config": "^5.0.1", "@types/better-sqlite3": "^7.6.13", "@types/node": "^22.10.7", "@types/ws": "^8.5.13", "drizzle-kit": "^0.31.1", "npm-run-all2": "^8.0.1", "prettier": "^3.4.2", "smee-client": "^4.1.2", "tsup": "^8.3.5", "typescript": "~5.8.3", "zod": "^3.24.4"}, "scripts": {"sapphire": "sapphire", "generate": "sapphire generate", "build": "tsup", "watch": "tsup --watch", "start": "node dist/mod.js", "dev": "tsup --watch --onSuccess \"node ./dist/mod.js\"", "format": "prettier --write \"src-js/**/*.ts\"", "typecheck": "tsc --noEmit", "smee:webhook": "pnpm smee --url https://smee.io/6hYErHvrIhWH3D67 --target http://localhost:3000/webhook", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "prettier": "@sapphire/prettier-config"}