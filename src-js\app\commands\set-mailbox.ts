import { ApplyOptions } from '@sapphire/decorators';
import { Command } from '@sapphire/framework';
import { ActionRowBuilder, ButtonBuilder, ChannelType, MessageFlags, PermissionFlagsBits, TextChannel } from 'discord.js';
import { TicketCreateButton } from '../components/buttons/ticket-create';
import { TicketCreateEmbed } from '../components/embeds/ticket';

@ApplyOptions<Command.Options>({
	name: 'set-mailbox',
	description: `configure's the mailbox channel`,
	requiredUserPermissions: PermissionFlagsBits.Administrator
})
export class SetMailboxCommand extends Command {
	public override registerApplicationCommands(registry: Command.Registry) {
		registry.registerChatInputCommand((builder) =>
			builder
				.setName(this.name)
				.setDescription(this.description)
				.addChannelOption((option) =>
					option
						.setName('mailbox')
						.setDescription('the channel to create tickets from')
						.addChannelTypes(ChannelType.GuildText)
						.setRequired(true)
				)
		);
	}

	public override async chatInputRun(interaction: Command.ChatInputCommandInteraction) {
		const row = new ActionRowBuilder<ButtonBuilder>().addComponents(TicketCreateButton);

		const channel = interaction.options.getChannel('mailbox') as TextChannel;

		await channel.permissionOverwrites.edit(channel.guildId, { SendMessages: false });

		const messages = await channel.messages.fetch();

		await Promise.all(messages.map((message) => message.delete()));

		await channel.send({ embeds: [TicketCreateEmbed], components: [row] });

		return interaction.reply({ content: 'mailbox channel is configured!', flags: MessageFlags.Ephemeral });
	}
}
