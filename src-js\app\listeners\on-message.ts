import { ApplyOptions } from '@sapphire/decorators';
import { Events, Listener } from '@sapphire/framework';
import { ChannelType, Message } from 'discord.js';
import * as regex from '../../utils/regex';

@ApplyOptions<Listener.Options>({
	event: Events.MessageCreate
})
export class OnMessage extends Listener<typeof Events.MessageCreate> {
	public override async run(message: Message) {
		if (!message.author.bot && message.channel.type === ChannelType.GuildText && message.channel.name.match(regex.snowflake)) {
			const ticket = await this.container.tickets.fetch(message.channel.name);

			if (ticket) {
				if (message.content) {
					await this.container.trengo.sendMessage({
						contact: { name: ticket.contactName, identifier: ticket.contactId },
						content: message.content
					});
				}

				for (const attachment of message.attachments.values()) {
					await this.container.trengo.sendMessage({
						contact: { name: ticket.contactName, identifier: ticket.contactId },
						content: attachment.url
					});
				}
			}
		}
	}
}
