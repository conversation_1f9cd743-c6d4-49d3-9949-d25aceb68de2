use serenity::async_trait;
use serenity::model::channel::{ChannelType, Message};
use serenity::prelude::*;

use crate::{
    ENV, regex,
    tickets::TICKETS,
    trengo::{self, StoreCustomChannelMessageBody},
};

pub struct Handler;

#[async_trait]
impl EventHandler for Handler {
    async fn message(&self, ctx: Context, msg: Message) {
        if msg.author.bot {
            return;
        }

        let guild_id = match msg.guild_id {
            Some(id) => id,
            None => return,
        };

        let channel_name = {
            let guild = match ctx.cache.guild(guild_id) {
                Some(guild) => guild,
                None => return,
            };

            let channel = match guild.channels.get(&msg.channel_id) {
                Some(channel) => channel,
                None => return,
            };

            if channel.kind != ChannelType::Text || channel.name.chars().all(|c| c.is_numeric()) {
                return;
            }

            channel.name.clone()
        };

        if !regex::SNOWFLAKE.is_match(&channel_name) {
            return;
        }

        let user_id = match channel_name.parse::<u64>() {
            Ok(id) => id,
            Err(_) => return,
        };

        let (contact_identifier, contact_name) = match TICKETS.fetch(user_id) {
            Some(ticket) => (
                ticket.contact_identifier.clone(),
                ticket.contact_name.clone(),
            ),
            None => return,
        };

        if !msg.content.is_empty() {
            let body = StoreCustomChannelMessageBody {
                channel: ENV.trengo_custom_channel_token.clone(),
                contact_identifier: contact_identifier.clone(),
                contact_name: contact_name.clone(),
                content: msg.content,
            };

            if let None = trengo::store_custom_channel_message(body).await {
                println!("error while sending message to trengo");
            }
        }

        for attachment in msg.attachments {
            let body = StoreCustomChannelMessageBody {
                channel: ENV.trengo_custom_channel_token.clone(),
                contact_identifier: contact_identifier.clone(),
                contact_name: contact_name.clone(),
                content: attachment.url,
            };

            if let None = trengo::store_custom_channel_message(body).await {
                println!("error while sending message to trengo");
            }
        }
    }
}
