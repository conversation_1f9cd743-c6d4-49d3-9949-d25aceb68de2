use rocket::{State, futures::future::join_all, post, serde::json::<PERSON><PERSON>};
use serde::Deserialize;
use serenity::all::{
    ChannelId, ChannelType, CreateAttachment, CreateMessage, GuildId, PermissionOverwrite,
    PermissionOverwriteType, UserId, builder::CreateChannel,
};

use crate::{
    RocketState, permissions,
    tickets::{TICKETS, TicketUpdate},
    trengo,
};

#[derive(Debug, Deserialize)]
pub struct WebhookPayload<'a> {
    message_id: &'a str,
    ticket_id: &'a str,
    contact_identifier: Option<&'a str>,
}

#[post("/webhook", data = "<payload>")]
pub async fn handler<'a>(payload: Json<WebhookPayload<'a>>, state: &State<RocketState>) {
    println!("recv /webhook with payload: {:?}", payload);

    if let Some(contact_identifier) = payload.contact_identifier {
        if contact_identifier.starts_with("custom-") {
            if let Ok(contact_identifier) = contact_identifier.parse::<u64>() {
                let mut ticket = match TICKETS.fetch(contact_identifier) {
                    Some(ticket) => ticket,
                    None => return,
                };

                let guild = match state
                    .discord_http
                    .get_guild(GuildId::new(ticket.guild_id))
                    .await
                    .ok()
                {
                    Some(guild) => guild,
                    None => return,
                };

                if let Some(mut channels) = guild.channels(&state.discord_http).await.ok() {
                    if !channels.contains_key(&ChannelId::new(ticket.channel_id)) {
                        let everyone_permissions = PermissionOverwrite {
                            allow: permissions::ticket_channel::EVERYONE_ALLOW,
                            deny: permissions::ticket_channel::EVERYONE_DENY,
                            kind: PermissionOverwriteType::Role(guild.id.everyone_role()),
                        };

                        let user_permissions = PermissionOverwrite {
                            allow: permissions::ticket_channel::USER_ALLOW,
                            deny: permissions::ticket_channel::USER_DENY,
                            kind: PermissionOverwriteType::Member(UserId::new(ticket.user_id)),
                        };

                        let builder = CreateChannel::new(ticket.contact_identifier)
                            .kind(ChannelType::Text)
                            .category(ChannelId::new(ticket.category_id))
                            .permissions(vec![everyone_permissions, user_permissions]);

                        let channel = guild
                            .create_channel(&state.discord_http, builder)
                            .await
                            .ok();

                        if channel.is_none() {
                            return;
                        }

                        let new_channel = channel.unwrap();

                        TICKETS.update(
                            ticket.user_id,
                            TicketUpdate {
                                channel_id: Some(new_channel.id.get()),
                                ..Default::default()
                            },
                        );

                        ticket.channel_id = new_channel.id.get();

                        channels.insert(new_channel.id, new_channel);
                    }

                    let channel = match channels.get(&ChannelId::new(ticket.channel_id)) {
                        Some(channel) => channel,
                        None => return,
                    };

                    match trengo::fetch_message(payload.ticket_id, payload.message_id).await {
                        Some(message) => {
                            let attachments =
                                join_all(message.attachment_urls.into_iter().map(async |url| {
                                    CreateAttachment::url(&state.discord_http, &url).await
                                }))
                                .await
                                .into_iter()
                                .filter(|attachment| attachment.is_ok())
                                .map(|attachment| attachment.unwrap())
                                .collect::<Vec<_>>();

                            let builder = CreateMessage::new()
                                .content(message.content)
                                .add_files(attachments);

                            match channel.send_message(&state.discord_http, builder).await {
                                Ok(message) => {
                                    println!("message sent: {:?}", message);
                                }
                                Err(why) => {
                                    println!("error while sending message to discord: {:?}", why);
                                }
                            }
                        }
                        None => {
                            println!("error while fetching message from trengo");
                        }
                    }
                }
            }
        }
    }
}
