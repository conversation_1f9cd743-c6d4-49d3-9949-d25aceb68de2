import { container } from '@sapphire/framework';
import { eq } from 'drizzle-orm';
import { Ticket, tickets } from './database/schema';

export class TicketsService {
	#tickets: Map<string, Ticket>;

	public constructor() {
		this.#tickets = new Map();
	}

	/**
	 * fetches a ticket.
	 *
	 * @param id - a user-id or contact-id to get tickets data for.
	 * @returns the ticket if found, otherwise null.
	 */
	public async fetch(id: string): Promise<Ticket | null> {
		const ticket = this.#tickets.get(id);

		if (ticket) return ticket;

		let fetched: Ticket | undefined;

		if (id.startsWith('custom-')) {
			fetched = await container.db.query.tickets.findFirst({ where: eq(tickets.contactId, id) });
		} else {
			fetched = await container.db.query.tickets.findFirst({ where: eq(tickets.userId, id) });
		}

		if (fetched) {
			this.#tickets.set(fetched.userId, fetched);
			this.#tickets.set(fetched.contactId, fetched);

			return fetched;
		}

		return null;
	}

	/**
	 * updates a ticket data.
	 *
	 * @param id - a user-id or contact-id to set tickets data for.
	 * @param values - a tickets data to set.
	 * @returns the ticket if updated, otherwise null.
	 */
	public async update(id: string, values: Partial<Pick<Ticket, 'guildId' | 'categoryId' | 'channelId' | 'contactName'>>): Promise<Ticket | null> {
		const ticket = await this.fetch(id);

		if (ticket) {
			if (id.startsWith('custom-')) {
				await container.db.update(tickets).set(values).where(eq(tickets.contactId, id)).execute();
			} else {
				await container.db.update(tickets).set(values).where(eq(tickets.userId, id)).execute();
			}

			ticket.guildId = values.guildId ?? ticket.guildId;
			ticket.categoryId = values.categoryId ?? ticket.categoryId;
			ticket.channelId = values.channelId ?? ticket.channelId;
			ticket.contactName = values.contactName ?? ticket.contactName;
		}

		return ticket;
	}

	/**
	 * inserts a new ticket.
	 *
	 * @param values - a ticket data to insert.
	 * @returns the inserted ticket if inserted, otherwise null.
	 */
	public async insert(values: Omit<Ticket, 'id'>): Promise<Ticket | null> {
		const ticket = (await container.db.insert(tickets).values(values).returning().execute())[0];

		if (ticket) {
			this.#tickets.set(values.userId, ticket);
			this.#tickets.set(values.contactId, ticket);

			return ticket;
		}

		return null;
	}
}

declare module '@sapphire/framework' {
	interface Container {
		tickets: TicketsService;
	}
}
