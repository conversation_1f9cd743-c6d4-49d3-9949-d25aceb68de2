import { LogLevel } from '@sapphire/framework';
import '@sapphire/plugin-api/register';
import '@sapphire/plugin-editable-commands/register';
import '@sapphire/plugin-logger/register';
import '@sapphire/plugin-subcommands/register';
import { GatewayIntentBits, Partials } from 'discord.js';
import dotenv from 'dotenv';
import { Client } from './services/discordjs';

const main = async () => {
	dotenv.config();

	const client = new Client({
		shards: 'auto',
		logger: { level: LogLevel.Debug },
		intents: [
			GatewayIntentBits.DirectMessageReactions,
			GatewayIntentBits.DirectMessages,
			GatewayIntentBits.GuildModeration,
			GatewayIntentBits.GuildExpressions,
			GatewayIntentBits.GuildMembers,
			GatewayIntentBits.GuildMessageReactions,
			GatewayIntentBits.GuildMessages,
			GatewayIntentBits.Guilds,
			GatewayIntentBits.GuildVoiceStates,
			GatewayIntentBits.MessageContent
		],
		partials: [Partials.Channel],
		api: {
			origin: '*',
			listenOptions: { port: 3000 }
		}
	});

	try {
		client.logger.info('logging in');

		await client.login();

		client.logger.info('logged in');
	} catch (error) {
		client.logger.fatal(error);

		await client.destroy();

		process.exit(1);
	}
};

void main();
