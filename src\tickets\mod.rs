use std::collections::HashMap;
use std::sync::RwLock;

use once_cell::sync::<PERSON>zy;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Ticket {
    pub id: u64,
    pub user_id: u64,
    pub guild_id: u64,
    pub category_id: u64,
    pub channel_id: u64,
    pub contact_name: String,
    pub contact_identifier: String,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct TicketUpdate {
    pub guild_id: Option<u64>,
    pub category_id: Option<u64>,
    pub channel_id: Option<u64>,
    pub contact_name: Option<String>,
}

pub struct Tickets {
    tickets: RwLock<HashMap<u64, Ticket>>,
}

impl Tickets {
    pub fn new() -> Self {
        Self {
            tickets: RwLock::new(HashMap::new()),
        }
    }

    pub fn fetch(&self, id: u64) -> Option<Ticket> {
        if let Ok(tickets) = self.tickets.read() {
            tickets.get(&id).cloned()
        } else {
            None
        }
    }

    pub fn insert(&self, ticket: Ticket) {
        if let Ok(mut tickets) = self.tickets.write() {
            tickets.insert(ticket.id, ticket);
        }
    }

    pub fn update(&self, id: u64, update: TicketUpdate) -> Option<Ticket> {
        if let Ok(mut tickets) = self.tickets.write() {
            if let Some(ticket) = tickets.get_mut(&id) {
                if let Some(guild_id) = update.guild_id {
                    ticket.guild_id = guild_id;
                }

                if let Some(category_id) = update.category_id {
                    ticket.category_id = category_id;
                }

                if let Some(channel_id) = update.channel_id {
                    ticket.channel_id = channel_id;
                }

                if let Some(contact_name) = update.contact_name {
                    ticket.contact_name = contact_name;
                }

                return Some(ticket.clone());
            }
        }

        None
    }
}

pub static TICKETS: Lazy<Tickets> = Lazy::new(Tickets::new);
