{"version": "6", "dialect": "sqlite", "id": "151e6dd9-388b-4d44-852d-93ca8f60563b", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"tickets": {"name": "tickets", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "guild_id": {"name": "guild_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "category_id": {"name": "category_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "channel_id": {"name": "channel_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "contact_id": {"name": "contact_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "contact_name": {"name": "contact_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"tickets_user_id_unique": {"name": "tickets_user_id_unique", "columns": ["user_id"], "isUnique": true}, "tickets_guild_id_unique": {"name": "tickets_guild_id_unique", "columns": ["guild_id"], "isUnique": true}, "tickets_category_id_unique": {"name": "tickets_category_id_unique", "columns": ["category_id"], "isUnique": true}, "tickets_channel_id_unique": {"name": "tickets_channel_id_unique", "columns": ["channel_id"], "isUnique": true}, "tickets_contact_id_unique": {"name": "tickets_contact_id_unique", "columns": ["contact_id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}