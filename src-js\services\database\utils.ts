import { Database } from 'better-sqlite3';
import { BetterSQLite3Database, drizzle } from 'drizzle-orm/better-sqlite3';
import * as schema from './schema';

export const createDatabase = () => {
	return drizzle({ connection: { source: process.env.DATABASE_URL }, schema });
};

declare module '@sapphire/framework' {
	interface Container {
		db: BetterSQLite3Database<typeof schema> & { $client: Database };
	}
}
