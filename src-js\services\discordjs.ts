import { ApplicationCommandRegistries, RegisterBehavior, SapphireClient } from '@sapphire/framework';
import { container, getRootData } from '@sapphire/pieces';
import * as colorette from 'colorette';
import { ClientOptions } from 'discord.js';
import path from 'node:path';
import { inspect } from 'node:util';
import { EnvSchema } from '../utils/env';
import { createDatabase } from './database/utils';
import { TicketsService } from './tickets';
import { TrengoService } from './trengo';

export class Client extends SapphireClient {
	public constructor(options: ClientOptions) {
		super(options);

		this.stores.registerPath(path.join(getRootData().root, 'app'));

		EnvSchema.parse(process.env);

		container.trengo = new TrengoService();

		container.tickets = new TicketsService();

		container.db = createDatabase();

		inspect.defaultOptions.depth = 1;

		colorette.createColors({ useColor: true });

		ApplicationCommandRegistries.setDefaultGuildIds([process.env.DISCORD_GUILD_ID]);
		ApplicationCommandRegistries.setDefaultBehaviorWhenNotIdentical(RegisterBehavior.BulkOverwrite);
	}
}
